package com.gy.server.annotation;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import com.gy.server.core.MessageConstant;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.stat.MethodInvokeStatisticsHandlerManager;
import com.gy.server.utils.EmbeddedLogger;

import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;

/**
 * <AUTHOR> - [Create on 2021/02/24 17:01]
 */
@SuppressWarnings("unused")
public class AnnotationMethodCacheManager {

    private static final Map<String, Method> METHODS = new ConcurrentHashMap<>();
    private static final Map<Method, MethodInvokeType> METHOD_INVOKE_TYPES = new ConcurrentHashMap<>();
    private static final Map<Method, Boolean> METHOD_PLAYER_ONLINE_ONLY = new ConcurrentHashMap<>();

    private AnnotationMethodCacheManager() {}

    /**
     * 默认注册方法，默认包名：com.gy.server
     */
    public static void register(MessageServerType curMessageServerType) {
        register("com.gy.server", curMessageServerType);
    }

    /**
     * 限制说明：
     * <p>1、类名后缀必须为{@link MessageConstant#MESSAGE_SERVICE_NAME_POSTFIX}；
     * <p>2、服务方法必须为static；
     * <p>3、methodId保证项目内唯一；
     * <p>4、参数数量必须为2，其中第一个参数类型必须为{@link CommandRequest}或其子类，第二个参数类型必须为{@link CommandRequestParams}；
     */
    public static void register(String packageName, MessageServerType curMessageServerType) {
        Reflections reflections = new Reflections(packageName);

        Optional.ofNullable(reflections.getTypesAnnotatedWith(MessageServiceBean.class))
                .ifPresent(classes -> classes.stream()
                        .filter(messageService -> {
                            MessageServiceBean annotation = messageService.getAnnotation(MessageServiceBean.class);
                            if (annotation.messageServerType() == curMessageServerType) {
                                return true;
                            }

                            MessageServerType[] messageServerTypes = annotation.type2();
                            if (Objects.nonNull(messageServerTypes)) {
                                for (MessageServerType messageServerType : messageServerTypes) {
                                    if (messageServerType == curMessageServerType) {
                                        return true;
                                    }
                                }
                            }

                            return false;
                        })
                        .forEach(messageService -> {
                            String serviceSimpleName = messageService.getSimpleName();
                            if (!serviceSimpleName.endsWith(MessageConstant.MESSAGE_SERVICE_NAME_POSTFIX)) {
                                throw new RuntimeException(String.format("annotation cache manager register error, service name postfix must be %s, illegal service name: %s.", MessageConstant.MESSAGE_SERVICE_NAME_POSTFIX, messageService.getName()));
                            }

                            Arrays.stream(messageService.getDeclaredMethods())
                                    .filter(method -> Modifier.isStatic(method.getModifiers()))
                                    .filter(method -> method.isAnnotationPresent(MessageMethod.class))
                                    .forEach(method -> {
                                        MessageMethod annotation = method.getAnnotation(MessageMethod.class);
                                        String id = StringUtils.isNotBlank(annotation.id()) ? annotation.id() : serviceSimpleName + "." + method.getName();

                                        // 不允许存在重复的关键码
                                        if (METHODS.containsKey(id)) {
                                            throw new RuntimeException("annotation cache manager register error, found duplicated method id: " + id);
                                        }

                                        int parameterCount = method.getParameterCount();
                                        // 参数数量不符合要求
                                        if (parameterCount != 2) {
                                            throw new RuntimeException(String.format("annotation cache manager register error, parameter count must be 2, [%s.%s]", serviceSimpleName, method.getName()));
                                        }

                                        Class<?>[] parameterTypes = method.getParameterTypes();
                                        // 参数类型不符合要求
                                        if (!CommandRequest.class.isAssignableFrom(parameterTypes[0])
                                                || !CommandRequestParams.class.isAssignableFrom(parameterTypes[1])) {
                                            throw new RuntimeException(String.format("annotation cache manager register error, parameter type must be CommandRequest And CommandRequestParams, [%s.%s]", serviceSimpleName, method.getName()));
                                        }

                                        method.setAccessible(true);
                                        METHODS.put(id, method);
                                        METHOD_INVOKE_TYPES.put(method, annotation.invokeType());
                                        METHOD_PLAYER_ONLINE_ONLY.put(method, annotation.playerOnlineOnly());
                                    });
                        }));

        METHODS.forEach((id, method) -> EmbeddedLogger.info(String.format("message method id: [%s]", id)));
    }

    public static Method getMethod(String id) {
        return METHODS.get(id);
    }

    public static MethodInvokeType getInvokeType(Method method) {
        return METHOD_INVOKE_TYPES.get(method);
    }

    public static boolean getPlayerOnlineOnly(Method method) {
        return METHOD_PLAYER_ONLINE_ONLY.get(method);
    }

    /**
     * 方法调用
     * <p>之所以抽取到这个方法内进行，是为了更方便打印日志
     * <p>后续如果有更高级的需求，可以通过扩展本方法，甚至可以接入类似AOP的组件、自定义组件等实现高级功能
     */
    public static void invoke(String methodId, Object obj, Object... params) throws InvocationTargetException, IllegalAccessException {
        long start = System.nanoTime();
        Method method = getMethod(methodId);
        if (Objects.isNull(method)) {
            EmbeddedLogger.error(String.format("invoke message method error, methodId not found: %s.", methodId));
            return;
        }

        method.invoke(obj, params);
        MethodInvokeStatisticsHandlerManager.ststistics(methodId, System.nanoTime() - start);
    }

}
