package com.gy.server.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.gy.server.core.MethodInvokeType;

/**
 * 服务方法注解
 * <AUTHOR> - [Create on 2021/02/24 16:37]
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@SuppressWarnings("unused")
public @interface MessageMethod {

    /**
     * 一个描述而已，无关紧要
     */
    String description() default "";

    /**
     * 方法唯一关键码，需在全项目中唯一
     * 当为默认值""时，默认为：简单类名 + 当前方法名，形如："A.test"
     */
    String id() default "";

    /**
     * 方法被调用时的规则
     */
    MethodInvokeType invokeType() default MethodInvokeType.sync;

    /**
     * True 玩家在线才调用，不在线就把消息存储起来等玩家登陆自己调用
     * False 正常调用
     */
    boolean playerOnlineOnly() default false;

}
