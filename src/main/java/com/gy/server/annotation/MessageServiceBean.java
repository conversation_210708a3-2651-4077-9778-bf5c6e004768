package com.gy.server.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.gy.server.core.MessageServerType;

/**
 * 服务类注解
 * <AUTHOR> - [Create on 2021/02/24 16:37]
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@SuppressWarnings("unused")
public @interface MessageServiceBean {

    /**
     * 一个描述而已，无关紧要
     */
    String description() default "";

    /**
     * 消息服务器类型
     */
    MessageServerType messageServerType();

    /**
     * 消息服务器类型2, 这是一个数组, 支持多种类型(为了不影响已有的服务而扩展类型1的做法)
     */
    MessageServerType[] type2() default {};

}
