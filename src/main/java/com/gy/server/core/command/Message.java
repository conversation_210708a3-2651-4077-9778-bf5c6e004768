package com.gy.server.core.command;

import java.io.Serializable;

/**
 * <AUTHOR> - [Create on 2021/03/02 11:04]
 */
@SuppressWarnings("rawtypes")
public class Message implements Serializable {
    private static final long serialVersionUID = 361679510159850071L;

    private CommandRequest commandRequest;

    private CommandRequestParams requestParams;

    public Message() {}

    public Message(CommandRequest commandRequest, CommandRequestParams requestParams) {
        this.commandRequest = commandRequest;
        this.requestParams = requestParams;
    }

    public Message(CommandRequest commandRequest) {
        this.commandRequest = commandRequest;
        this.requestParams = new CommandRequestParams(CommandRequestParams.NON_PARAMS);
    }

    public Message(CommandRequest commandRequest, Object[] params) {
        if(params == null || params.length == 0){
            this.commandRequest = commandRequest;
            this.requestParams = new CommandRequestParams(CommandRequestParams.NON_PARAMS);
        }else{
            this.commandRequest = commandRequest;
            this.requestParams = new CommandRequestParams(params);
        }
    }

    public void ack(){

    }

    /**
     * 当接收方不在线时，消息是否可以丢弃。默认true
     */
    public boolean canDiscard(){
        return commandRequest.canDiscard;
    }

    public CommandRequest getCommandRequest() {
        return commandRequest;
    }

    public void setCommandRequest(CommandRequest commandRequest) {
        this.commandRequest = commandRequest;
    }

    public CommandRequestParams getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(CommandRequestParams requestParams) {
        this.requestParams = requestParams;
    }
}
