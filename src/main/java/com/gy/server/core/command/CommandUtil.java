package com.gy.server.core.command;

import com.gy.server.core.MessageConstant;
import com.gy.server.core.MessageServerType;

/**
 * <AUTHOR> - [Create on 2021/03/03 10:48]
 */
@SuppressWarnings("unused")
public final class CommandUtil {

    private CommandUtil() {}

    public static int hashKeyToIndex(int hashKey, int size) {
        return Math.abs(hashKey % size);
    }

    public static ServerCommandRequest newServerCommandRequest(String methodId,
                                                               MessageServerType messageServerType,
                                                               int serverId) {
        return newServerCommandRequest(MessageConstant.DEFAULT_HASH_KEY, methodId, messageServerType, serverId, true);
    }

    public static ServerCommandRequest newServerCommandRequest(int hashKey,
                                                               String methodId,
                                                               MessageServerType messageServerType,
                                                               int serverId,
                                                               boolean canDiscard) {
        ServerCommandRequest request = new ServerCommandRequest();

        request.setHashKey(hashKey);
        request.setMethodId(methodId);
        request.setMessageServerType(messageServerType);
        request.setServerId(serverId);
        request.setCanDiscard(canDiscard);

        return request;
    }

    public static PlayerCommandRequest newPlayerCommandRequest(String methodId,
                                                               MessageServerType messageServerType,
                                                               int serverId,
                                                               long playerId,
                                                               long timestamp) {
        return newPlayerCommandRequest(MessageConstant.DEFAULT_HASH_KEY, methodId, messageServerType, serverId, playerId, timestamp, true);
    }

    public static PlayerCommandRequest newPlayerCommandRequest(int hashKey,
                                                               String methodId,
                                                               MessageServerType messageServerType,
                                                               int serverId,
                                                               long playerId,
                                                               long timestamp,
                                                               boolean canDiscard) {
        PlayerCommandRequest request = new PlayerCommandRequest();

        request.setHashKey(hashKey);
        request.setMethodId(methodId);
        request.setMessageServerType(messageServerType);
        request.setServerId(serverId);
        request.setPlayerId(playerId);
        request.setTimestamp(timestamp);
        request.setCanDiscard(canDiscard);

        return request;
    }

}
