package com.gy.server.core.command;

import com.gy.server.core.MessageConstant;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.RegisterSystem;
import com.gy.server.core.callback.CallbackType;
import com.gy.server.core.callback.MessageCallbackManager;
import com.gy.server.core.callback.MessageCallbackTask;
import com.gy.server.core.exception.MessageExceptionType;
import com.gy.server.core.util.MessageUtil;

import java.io.Serializable;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR> - [Create on 2021/02/26 14:37]
 */
@SuppressWarnings("unused")
public class CommandRequest implements Serializable {

    private static final long serialVersionUID = 362679510159850071L;

    /**
     * 请求ID，进程内唯一
     */
    private long id = MessageUtil.genNextRequestId();

    /**
     * 根据此值进行指定策略的分发
     */
    protected int hashKey = MessageConstant.DEFAULT_HASH_KEY;

    /**
     * 方法ID指定了一个具体的方法，从而进行调度
     */
    protected String methodId;

    /**
     * 发送请求时的时间戳，单位：毫秒
     */
    protected long sendTimestamp = System.currentTimeMillis();

    /**
     * 请求包是否允许抛弃。一般而言，重要的信息定义为不可抛弃
     */
    protected boolean canDiscard = true;

    /**
     * 默认为请求方式
     */
    protected CommandType commandType = CommandType.req;

    /**
     * 回调ID，当回调到来时，根据此ID获取任务上下文进行调度
     */
    protected String callbackId = MessageConstant.DEFAULT_CALLBACK_ID;

    /**
     * 回调参数列表
     */
    protected volatile List<Object> callbackParams = new CopyOnWriteArrayList<>();

    /**
     * 请求方渠道名和serverId
     */
    @Deprecated
    protected String callbackStreamName = "";
    @Deprecated
    protected int callbackServerId = 0;

    /**
     * 响应方渠道名和serverId
     */
    protected String remoteStreamName = "";
    protected int remoteServerId = 0;

    protected MessageServerType senderServerType = RegisterSystem.serverType;
    protected int senderServerId = RegisterSystem.serverId;

    protected String messageExceptionType = MessageExceptionType.TYPE_NONE;

    public CommandRequest() {
    }

    /**
     * 回调类型，默认为被动
     */
    protected volatile CallbackType callbackType = CallbackType.passive;

    public long getId() {
        return id;
    }

    public int getHashKey() {
        return hashKey;
    }

    public void setHashKey(int hashKey) {
        this.hashKey = hashKey;
    }

    public String getMethodId() {
        return methodId;
    }

    public void setMethodId(String methodId) {
        this.methodId = methodId;
    }

    public long getSendTimestamp() {
        return sendTimestamp;
    }

    public void setSendTimestamp(long sendTimestamp) {
        this.sendTimestamp = sendTimestamp;
    }

    public boolean isCanDiscard() {
        return canDiscard;
    }

    public void setCanDiscard(boolean canDiscard) {
        this.canDiscard = canDiscard;
    }

    public void setCallbackId(String callbackId) {
        this.callbackId = callbackId;
    }

    public String getCallbackId() {
        return callbackId;
    }

    public CommandType getCommandType() {
        return commandType;
    }

    public void setCommandType(CommandType commandType) {
        this.commandType = commandType;
    }

    public void addCallbackParam(Object param) {
        this.callbackParams.add(param);
    }

    public List<Object> getCallbackParams() {
        return callbackParams;
    }

    public String getCallbackStreamName() {
        return callbackStreamName;
    }

    public void setCallbackStreamName(String callbackStreamName) {
        this.callbackStreamName = callbackStreamName;
    }

    public int getCallbackServerId() {
        return callbackServerId;
    }

    public void setCallbackServerId(int callbackServerId) {
        this.callbackServerId = callbackServerId;
    }

    public String getRemoteStreamName() {
        return remoteStreamName;
    }

    public void setRemoteStreamName(String remoteStreamName) {
        this.remoteStreamName = remoteStreamName;
    }

    public int getRemoteServerId() {
        return remoteServerId;
    }

    public void setRemoteServerId(int remoteServerId) {
        this.remoteServerId = remoteServerId;
    }

    public String getMessageExceptionType() {
        return messageExceptionType;
    }

    public void setMessageExceptionType(String messageExceptionType) {
        this.messageExceptionType = messageExceptionType;
    }

    public MessageServerType getSenderServerType() {
        return senderServerType;
    }

    public void setSenderServerType(MessageServerType senderServerType) {
        this.senderServerType = senderServerType;
    }

    public int getSenderServerId() {
        return senderServerId;
    }

    public void setSenderServerId(int senderServerId) {
        this.senderServerId = senderServerId;
    }

    public CallbackType getCallbackType() {
        return callbackType;
    }

    public void setCallbackType(CallbackType callbackType) {
        this.callbackType = callbackType;
    }

    public void assembleCallbackInfo() {
        setCommandType(CommandType.callback);
        setRemoteStreamName(RegisterSystem.callbackStreamName);
        setRemoteServerId(RegisterSystem.callbackServerId);
    }

    public void callBack(){
        if(commandType != CommandType.req){
            throw new IllegalArgumentException("非请求消息不能回调");
        }

        this.assembleCallbackInfo();

        MessageServerType targetServerType = senderServerType;
        int targetServerId = senderServerId;

        //重置发送者数据
        this.senderServerType = RegisterSystem.serverType;
        this.senderServerId = RegisterSystem.serverId;

        RegisterSystem.sender.sendToSingle(new Message(this), targetServerType, targetServerId);
    }

    /**
     * 当前请求对应一个task的快捷方法
     */
    public void callback(MessageCallbackTask task) {
        this.callbackId = task.getId();
        this.callbackStreamName = RegisterSystem.callbackStreamName;
        this.callbackServerId = RegisterSystem.callbackServerId;

        MessageCallbackManager.addTask(task);
    }

    public <T extends CommandRequest> T copy(){return null;};

    @Override
    public String toString() {
        return "CommandRequest{" +
                "id=" + id +
                ", hashKey=" + hashKey +
                ", methodId='" + methodId + '\'' +
                ", sendTimestamp=" + sendTimestamp +
                ", canDiscard=" + canDiscard +
                ", commandType=" + commandType +
                ", callbackId='" + callbackId + '\'' +
                ", callbackParams=" + callbackParams +
                ", callbackStreamName='" + callbackStreamName + '\'' +
                ", callbackServerId=" + callbackServerId +
                ", remoteStreamName='" + remoteStreamName + '\'' +
                ", remoteServerId=" + remoteServerId +
                ", messageExceptionType='" + messageExceptionType + '\'' +
                '}';
    }

}
