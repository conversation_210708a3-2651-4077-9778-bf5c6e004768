package com.gy.server.core.command;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;

/**
 * @program: message-nats
 * @description: 用于序列化Message对象
 * @author: <PERSON><PERSON>
 * @create: 2025/6/11
 **/
public class MessageBytes {

    @Protobuf(order = 1)
    private byte[] keyBytes;

    @Protobuf(order = 2)
    private byte[] paramBytes;

    public MessageBytes() {}

    public MessageBytes(byte[] keyBytes, byte[] paramBytes) {
        this.keyBytes = keyBytes;
        this.paramBytes = paramBytes;
    }

    public MessageBytes(Message message){

    }

    public byte[] getKeyBytes() {
        return keyBytes;
    }

    public void setKeyBytes(byte[] keyBytes) {
        this.keyBytes = keyBytes;
    }

    public byte[] getParamBytes() {
        return paramBytes;
    }

    public void setParamBytes(byte[] paramBytes) {
        this.paramBytes = paramBytes;
    }
}
