package com.gy.server.core.command;

/**
 * <AUTHOR> - [Create on 2021/02/26 16:11]
 */
@SuppressWarnings("unused")
public class CommandRequestParams implements java.io.Serializable {
    private static final long serialVersionUID = 3679510159850071L;
    public static final Object[] NON_PARAMS = new Object[0];

    private Object[] params;

    public CommandRequestParams() {
        this(NON_PARAMS);
    }

    public CommandRequestParams(Object... params) {
        this.params = params;
    }

    @SuppressWarnings("unchecked")
    public <P> P getParam(final int index) {
        return (P) params[index];
    }

    public Object[] getParams() {
        return params;
    }

    public void setParams(Object[] params) {
        this.params = params;
    }
}
