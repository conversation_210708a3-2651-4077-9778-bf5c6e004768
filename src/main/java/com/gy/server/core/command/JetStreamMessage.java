package com.gy.server.core.command;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * @program: message-nats
 * @description: JetStream message 实现
 * @author: <PERSON><PERSON>
 * @create: 2025/6/11
 **/
public class JetStreamMessage extends Message {

    @JsonIgnore
    private io.nats.client.Message natsMessage;

    @Override
    public void ack() {
        natsMessage.ack();
    }

    public io.nats.client.Message getNatsMessage() {
        return natsMessage;
    }

    public void setNatsMessage(io.nats.client.Message natsMessage) {
        this.natsMessage = natsMessage;
    }
}
