package com.gy.server.core.command;

import com.gy.server.core.MessageServerType;

/**
 * <AUTHOR> - [Create on 2021/03/01 18:08]
 */
@SuppressWarnings("unused")
public class ServerCommandRequest extends CommandRequest {

    private static final long serialVersionUID = 1015786523750017449L;

    protected MessageServerType messageServerType;
    protected int serverId;

    public MessageServerType getMessageServerType() {
        return messageServerType;
    }

    public void setMessageServerType(MessageServerType messageServerType) {
        this.messageServerType = messageServerType;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    @SuppressWarnings("unchecked")
    @Override
    public ServerCommandRequest copy() {
        ServerCommandRequest result = new ServerCommandRequest();

        result.hashKey = this.hashKey;
        result.methodId = this.methodId;
        result.sendTimestamp = this.sendTimestamp;
        result.canDiscard = this.canDiscard;
        result.commandType = this.commandType;
        result.callbackId = this.callbackId;
        result.callbackParams = this.callbackParams;
        result.callbackStreamName = this.callbackStreamName;
        result.callbackServerId = this.callbackServerId;
        result.remoteStreamName = this.remoteStreamName;
        result.remoteServerId = this.remoteServerId;
        result.messageExceptionType = this.messageExceptionType;
        result.callbackType = this.callbackType;
        result.messageServerType = this.messageServerType;
        result.serverId = this.serverId;

        return result;
    }

    @Override
    public String toString() {
        return "ServerCommandRequest{" +
                "hashKey=" + hashKey +
                ", methodId='" + methodId + '\'' +
                ", sendTimestamp=" + sendTimestamp +
                ", canDiscard=" + canDiscard +
                ", commandType=" + commandType +
                ", callbackId='" + callbackId + '\'' +
                ", callbackParams=" + callbackParams +
                ", callbackStreamName='" + callbackStreamName + '\'' +
                ", callbackServerId=" + callbackServerId +
                ", remoteStreamName='" + remoteStreamName + '\'' +
                ", remoteServerId=" + remoteServerId +
                ", messageServerType=" + messageServerType +
                ", serverId=" + serverId +
                '}';
    }

}
