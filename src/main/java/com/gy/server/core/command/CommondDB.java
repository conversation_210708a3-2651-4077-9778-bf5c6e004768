package com.gy.server.core.command;

import java.io.Serializable;

/**
 * 玩家离线时，命令的存储和调用
 */
public class CommondDB implements Serializable {

    private long createTime = System.currentTimeMillis();

    private PlayerCommandRequest req;

    private Object[] params;

    public CommondDB(PlayerCommandRequest req, Object[] params) {
        this.req = req;
        this.params = params;
    }

    public CommondDB() {
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public PlayerCommandRequest getReq() {
        return req;
    }

    public void setReq(PlayerCommandRequest req) {
        this.req = req;
    }

    public Object[] getParams() {
        return params;
    }

    public void setParams(Object[] params) {
        this.params = params;
    }
}
