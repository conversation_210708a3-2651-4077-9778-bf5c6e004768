package com.gy.server.core.command;

/**
 * <AUTHOR> - [Create on 2021/03/01 18:09]
 */
@SuppressWarnings("unused")
public class PlayerCommandRequest extends ServerCommandRequest {

    private static final long serialVersionUID = 9020507419636919939L;

    private long playerId;
    private long timestamp;

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public PlayerCommandRequest copy() {
        PlayerCommandRequest result = new PlayerCommandRequest();

        result.hashKey = this.hashKey;
        result.methodId = this.methodId;
        result.sendTimestamp = this.sendTimestamp;
        result.canDiscard = this.canDiscard;
        result.commandType = this.commandType;
        result.callbackId = this.callbackId;
        result.callbackParams = this.callbackParams;
        result.callbackStreamName = this.callbackStreamName;
        result.callbackServerId = this.callbackServerId;
        result.remoteStreamName = this.remoteStreamName;
        result.remoteServerId = this.remoteServerId;
        result.messageExceptionType = this.messageExceptionType;
        result.callbackType = this.callbackType;
        result.messageServerType = this.messageServerType;
        result.serverId = this.serverId;
        result.playerId = this.playerId;
        result.timestamp = this.timestamp;

        return result;
    }

    @Override
    public String toString() {
        return "PlayerCommandRequest{" +
                "hashKey=" + hashKey +
                ", methodId='" + methodId + '\'' +
                ", sendTimestamp=" + sendTimestamp +
                ", canDiscard=" + canDiscard +
                ", commandType=" + commandType +
                ", callbackId='" + callbackId + '\'' +
                ", callbackParams=" + callbackParams +
                ", callbackStreamName='" + callbackStreamName + '\'' +
                ", callbackServerId=" + callbackServerId +
                ", remoteStreamName='" + remoteStreamName + '\'' +
                ", remoteServerId=" + remoteServerId +
                ", playerId=" + playerId +
                ", timestamp=" + timestamp +
                ", messageServerType=" + messageServerType +
                ", serverId=" + serverId +
                '}';
    }
    
}
