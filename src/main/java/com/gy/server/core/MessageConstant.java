package com.gy.server.core;

/**
 * 消息系统常量
 * <AUTHOR> - [Create on 2021/02/24 18:03]
 */
public interface MessageConstant {

    int DEFAULT_STREAM_TRIM_LEN = 10000;                                // stream条目最大数，超过则被修剪
    boolean DEFAULT_STREAM_TRIM_STRICT = false;                         // stream条目达到上限时，是否强制立刻修剪
    int WORKER_QUEUE_CAPACITY = 8096;                                   // 工作者队列最大长度
    int DEFAULT_HASH_KEY = -1;
    int READ_STREAM_COUNT_FOR_EVERY_TIMES = 50;                         // 每次读取stream的条数
    long READ_STREAM_TIMEOUT = 10;                                      // 读取stream时的超时时长
    String MESSAGE_SERVICE_NAME_POSTFIX = "CommandService";             // 注解类名后缀
    long DISCARD_TIME = 5;                                              // 丢弃时间，当前时间 - 数据进队列时间超过这个时间的话将被丢弃。单位：分
    String DEFAULT_CALLBACK_ID = "";                                    // 默认回调ID

}
