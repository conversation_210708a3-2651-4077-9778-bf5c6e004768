package com.gy.server.core.nats;

import com.gy.server.core.RegisterSystem;
import com.gy.server.core.command.JetStreamMessage;
import com.gy.server.core.command.Message;
import com.gy.server.core.receive.AbstractStreamReceiver;
import com.gy.server.utils.EmbeddedLogger;
import com.gy.server.utils.qps.QPSManager;
import io.nats.client.Dispatcher;
import io.nats.client.JetStream;
import io.nats.client.MessageHandler;
import io.nats.client.PushSubscribeOptions;
import io.nats.client.api.ConsumerConfiguration;
import io.nats.client.api.DeliverPolicy;


/**
 * @program: message-nats
 * @description: nats实现的消息订阅器
 * @author: Huang.Xia
 * @create: 2025/6/11
 **/
public class NatsStreamReceiver extends AbstractStreamReceiver {

    private boolean isQueue;

    public NatsStreamReceiver(String streamName, String subjectName, String consumerGroup, String consumerName, boolean isQueue) {
        super(RegisterSystem.evnName + "_" + streamName, subjectName, RegisterSystem.evnName + "_" + consumerGroup, RegisterSystem.evnName + "_" + consumerName);
        this.isQueue = isQueue;
    }

    @Override
    protected void subStartup(Object... params) {
        DeliverPolicy deliverPolicy = isQueue? DeliverPolicy.All : DeliverPolicy.Last;

            //jetStream消息监听
            PushSubscribeOptions options = PushSubscribeOptions.builder()
                    .stream(streamName)                  // 绑定的 JetStream 名称
                    .deliverGroup(consumerGroup)
                    .durable(consumerGroup)
                    .configuration(ConsumerConfiguration.builder()
                            .deliverPolicy(deliverPolicy)
                            .maxAckPending(10000)
                            .build()
                    )
                    .build();

            try {
                JetStream js = NatsHelper.getJetStream();
                Dispatcher d = NatsHelper.getNatsConnection().createDispatcher();
                MessageHandler handler = (msg) -> {
                    try {
                        Message message = (Message) RegisterSystem.serialUtil.decodeMessage(msg.getData());
                        JetStreamMessage jmsg = new JetStreamMessage();
                        jmsg.setCommandRequest(message.getCommandRequest());
                        jmsg.setRequestParams(message.getRequestParams());
                        jmsg.setNatsMessage(msg);
                        receiveMessage(jmsg);

                        QPSManager.count("nats.jetstream.receive.all");
                        QPSManager.count("nats.jetstream.receive." + jmsg.getCommandRequest().getMethodId());



                    }catch (Exception e){
                        EmbeddedLogger.error("nats stream receiver error", e);
                        msg.ack();
                    }
                };

                EmbeddedLogger.info(options.toString());

                js.subscribe(subjectName, d, handler, false, options);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
    }

    @Override
    protected void subShutdown() {

    }
}
