package com.gy.server.core.nats;

import com.gy.server.core.RegisterSystem;
import com.gy.server.core.command.Message;
import com.gy.server.core.receive.AbstractStreamReceiver;
import com.gy.server.utils.EmbeddedLogger;
import com.gy.server.utils.qps.QPSManager;
import io.nats.client.Dispatcher;

/**
 * @program: message-nats
 * @description: nats实现的消息订阅器
 * @author: <PERSON><PERSON>
 * @create: 2025/6/11
 **/
public class NatsCoreReceiver extends AbstractStreamReceiver {
    public NatsCoreReceiver(String subjectName, String consumerGroup) {
        super("", subjectName, RegisterSystem.evnName+"." + consumerGroup, "");
    }

    @Override
    protected void subStartup(Object... params) {
            //core消息监听
            Dispatcher d = NatsHelper.getNatsConnection().createDispatcher((msg) -> {
                Message message = (Message) RegisterSystem.serialUtil.decodeMessage(msg.getData());
                receiveMessage(message);

                QPSManager.count("nats.core.receive.all");
                QPSManager.count("nats.core.receive." + message.getCommandRequest().getMethodId());
            });

            d.subscribe(subjectName, consumerGroup);
            EmbeddedLogger.info("nats core receiver started. subject: " + subjectName + ", consumerGroup: " + consumerGroup);
    }

    @Override
    protected void subShutdown() {

    }
}
