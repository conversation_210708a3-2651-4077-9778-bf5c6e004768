package com.gy.server.core.nats;

import com.gy.server.core.MessageServerType;
import com.gy.server.core.RegisterSystem;
import com.gy.server.core.command.Message;
import com.gy.server.core.dispatch.StreamDispatcher;
import com.gy.server.core.send.AbstractStreamSender;
import com.gy.server.utils.EmbeddedLogger;

import java.util.Collection;

/**
 * @program: message-nats
 * @description: nats实现的消息发送类
 * @author: <PERSON><PERSON>
 * @create: 2025/6/11
 **/
public class NatsSender extends AbstractStreamSender {
    public NatsSender(StreamDispatcher dispatcher) {
        super(dispatcher);
    }

    @Override
    public void subSendToSingle(Message message, MessageServerType serverType, int id) {
        byte[] data = RegisterSystem.serialUtil.encode(message);
        checkData(data, message.getCommandRequest().getMethodId());
        NatsHelper.publish(NatsHelper.getSingleSubject(serverType,id, message.canDiscard()), data, message.canDiscard());
    }

    @Override
    public void subSendToAny(Message message, MessageServerType serverType) {
        byte[] data = RegisterSystem.serialUtil.encode(message);
        checkData(data, message.getCommandRequest().getMethodId());
        NatsHelper.publish(NatsHelper.getCompeteSubject(serverType, message.canDiscard()), data, message.canDiscard());
    }

    @Override
    public void subSendToMany(Message message, MessageServerType serverType, Collection<Integer> ids) {
        byte[] data = RegisterSystem.serialUtil.encode(message);
        checkData(data, message.getCommandRequest().getMethodId());
        for(int id : ids){
            NatsHelper.publish(NatsHelper.getSingleSubject(serverType,id, message.canDiscard()), data, message.canDiscard());
        }
    }

    @Override
    public void subSendToAll(Message message, MessageServerType serverType) {
        byte[] data = RegisterSystem.serialUtil.encode(message);
        checkData(data, message.getCommandRequest().getMethodId());
        NatsHelper.publish(NatsHelper.getBroadcastSubject(serverType, message.canDiscard()), data, message.canDiscard());
    }

    private void checkData(byte[] data, String methodId){
        // 超过20M, 打印错误日志并阻止发送
        if (data.length >= 5 * 1024 * 1024) {
            EmbeddedLogger.error(String.format("stream sender found too big key request, stop sending now, methodId: %s, size: %s", methodId, data.length));
            throw new IllegalArgumentException("stream sender found too big key request, stop sending now, methodId: " + methodId + ", size: " + data.length);
        } /*超过5M, 打印警告日志*/ else if (data.length >= 1 * 1024 * 1024) {
            EmbeddedLogger.warn(String.format("stream sender found big key request, methodId: %s, size: %s", methodId, data.length));
        }
    }
}
