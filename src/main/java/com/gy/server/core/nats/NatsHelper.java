package com.gy.server.core.nats;

import com.gy.server.core.MessageServerType;
import com.gy.server.core.RegisterSystem;
import com.gy.server.core.receive.StreamReceiver;
import com.gy.server.utils.EmbeddedLogger;
import io.nats.client.*;
import io.nats.client.api.*;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;

/**
 * @program: message-nats
 * @description: nats工具类
 * @author: <PERSON><PERSON>
 * @create: 2025/6/11
 **/
public class NatsHelper {

    public static Connection natsConnection;
    public static JetStream jetStream;

    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    public static Connection getNatsConnection() {
        return natsConnection;
    }

    public static JetStream getJetStream() throws IOException {
        if (jetStream == null) {
            jetStream = getNatsConnection().jetStream();
        }
        return jetStream;
    }

    private static void startFlusher(){
        // 定时每10ms自动 flush
        scheduler.scheduleAtFixedRate(() -> {
            try {
                natsConnection.flush(Duration.ofMillis(10)); // flush with timeout
            }catch (TimeoutException e){
            } catch (Exception e) {
                e.printStackTrace();
            }
        }, 0, 10, TimeUnit.MILLISECONDS);
    }

    public static void init(String url) throws IOException, InterruptedException {
        natsConnection = Nats.connect(url);

        EmbeddedLogger.info("nats connection init ok.");
        startFlusher();
    }

    public static void init(String url, String username, String password) throws IOException, InterruptedException {
        Options options = new Options.Builder()
                .server(url)
                .userInfo(username, password)
                .traceConnection()
                .build();

        natsConnection = Nats.connect(options);
        EmbeddedLogger.info("nats connection init okk.");
        startFlusher();
    }

    public static void checkStream(String streamName, boolean isQueue, String... subjects){
        try {
            int nodeSize = natsConnection.getServerInfo().getConnectURLs().size();
            int replicaSize = 1;
            if(nodeSize > 1){
                replicaSize = 2;
            }
            JetStreamManagement jsm = natsConnection.jetStreamManagement();
            StreamInfo info = null;
            try{
                info = jsm.getStreamInfo(streamName);
            }catch (Exception e){};

            long maxBytes = isQueue ? 256*1024*1024L : 2*1024*1024*1024L;
            int maxMessage = isQueue ? 10000 : 40 * 10000;

            if (info == null) {
                RetentionPolicy retentionPolicy = isQueue? RetentionPolicy.WorkQueue : RetentionPolicy.Limits;
                StreamConfiguration streamConfig = StreamConfiguration.builder()
                        .name(streamName)
                        .retentionPolicy(retentionPolicy)
                        .subjects(subjects) // 绑定的 subject
                        .storageType(StorageType.File)
                        .maxBytes(maxBytes)
                        .maxMessagesPerSubject(50000)
                        .maximumMessageSize(8*1024*1024)
                        .maxMessages(maxMessage)
                        .replicas(replicaSize)
                        .maxAge(Duration.ofDays(2))
                        .build();
                try {
                    jsm.addStream(streamConfig);
                }catch (Exception e){
                    e.printStackTrace();
                }

                Thread.sleep(1000);
                //循环调用，多一次检查,尽可能纠正并发写入带来的覆盖问题
                checkStream(streamName, isQueue, subjects);
            } else {
                Set<String> subs = new HashSet<>();
                StreamConfiguration oldConfig = info.getConfiguration();
                subs.addAll(oldConfig.getSubjects());
                boolean containsAll = true;
                for (String subject : subjects) {
                    if (!subs.contains(subject)) {
                        containsAll = false;
                        break;
                    }
                }

                if (!containsAll) {
                    //加入自己的subject
                    for (String subject : subjects) {
                        subs.add(subject);
                    }

                    StreamConfiguration newConfig = StreamConfiguration.builder()
                            .name(oldConfig.getName())
                            .subjects(new ArrayList<>(subs))
                            .storageType(oldConfig.getStorageType())
                            .retentionPolicy(oldConfig.getRetentionPolicy())
                            .maxBytes(oldConfig.getMaxBytes())
                            .maxMessagesPerSubject(oldConfig.getMaxMsgsPerSubject())
                            .maximumMessageSize(oldConfig.getMaximumMessageSize())
                            .maxMessages(maxMessage)
                            .replicas(oldConfig.getReplicas())
                            .maxAge(oldConfig.getMaxAge())
                            .build();

                    jsm.updateStream(newConfig);  // 更新 Stream

                    Thread.sleep(1000);
                    //循环调用，多一次检查,尽可能纠正并发写入带来的覆盖问题
                    checkStream(streamName, isQueue, subjects);
                } else {
                    EmbeddedLogger.info("stream " + streamName + " : " + Arrays.toString(subjects) + " check ok.");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void checkStream(MessageServerType serverType, int serverId) throws JetStreamApiException, IOException {
        checkStream(RegisterSystem.evnName + "_" + serverType.name() + "_normal", false, getBroadcastSubject(serverType, false), getSingleSubject(serverType, serverId, false));
        checkStream(RegisterSystem.evnName + "_" + serverType.name()+"_queue", true, getCompeteSubject(serverType, false));
    }

    public static List<StreamReceiver> createReceiver(MessageServerType serverType, int serverId) throws JetStreamApiException, IOException {
        List<StreamReceiver> list = new ArrayList<>();

        //单播
        StreamReceiver receiver = new NatsCoreReceiver(getSingleSubject(serverType, serverId, true), serverType.name());
        list.add(receiver);
        receiver = new NatsStreamReceiver(serverType.name()+ "_normal", getSingleSubject(serverType, serverId, false),
                serverType.name() + "_single_js_" + serverId, serverType.name() + "_single_js_" + serverId, false);
        list.add(receiver);

        //广播
        receiver = new NatsCoreReceiver(getBroadcastSubject(serverType, true), serverType.name() + "_" + serverId);
        list.add(receiver);
        receiver = new NatsStreamReceiver(serverType.name()+ "_normal", getBroadcastSubject(serverType, false),
                serverType.name() + "_br_js_" + serverId, serverType.name() + "_br_js_" + serverId, false);
        list.add(receiver);

        //竞争
        receiver = new NatsCoreReceiver(getCompeteSubject(serverType, true), serverType.name() + "_any");
        list.add(receiver);
        receiver = new NatsStreamReceiver(serverType.name()+"_queue", getCompeteSubject(serverType, false),
                serverType.name() + "_any", serverType.name() + "_any_js", true);
        list.add(receiver);

        //创建stream
        checkStream(serverType, serverId);

        return list;
    }

    public static String getSingleSubject(MessageServerType serverType, int serverId, boolean canDiscard) {
        return RegisterSystem.evnName + "." + (canDiscard ? "core_" : "js_") + serverType.name() + "." + serverId;
    }

    public static String getBroadcastSubject(MessageServerType serverType, boolean canDiscard) {
        return RegisterSystem.evnName + "." + (canDiscard ? "core_" : "js_") + serverType.name() + "_all";
    }

    public static String getCompeteSubject(MessageServerType serverType, boolean canDiscard) {
        return RegisterSystem.evnName + "." + (canDiscard ? "core_" : "js_") + serverType.name() + "_any";
    }

    public static void publish(String subject, byte[] data, boolean canDiscard) {
        if (canDiscard) {
            getNatsConnection().publish(subject, data);
        } else {
            try {
                CompletableFuture<PublishAck> future = getJetStream().publishAsync(subject,data);
                future.exceptionally(ex-> {
                    EmbeddedLogger.error(ex + " subject : " + subject) ;
                    return null;
                });
            } catch (Exception e) {
                e.printStackTrace();
                EmbeddedLogger.error("publish error, subject: " + subject);
            }
        }
    }

    public static void shutDown() throws Exception {
        natsConnection.flush(Duration.ofSeconds(10));
        scheduler.shutdownNow();
    }


}
