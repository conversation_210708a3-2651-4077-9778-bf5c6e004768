package com.gy.server.core.work;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

import com.gy.server.utils.runner.RunnerManager;

/**
 * 工作者管理器
 * 一个进程只能有一批同一种类型的工作者，因此无法消费其他的数据结构
 * <AUTHOR> - [Create on 2021/03/04 19:44]
 */
@SuppressWarnings({"unused", "rawtypes"})
public final class WorkerManager {

    private static final AtomicBoolean INIT_WORKER = new AtomicBoolean(false);
    private static final Map<Integer, Worker> WORKERS = new ConcurrentHashMap<>();
    private static final List<Integer> WORK_IDS = new CopyOnWriteArrayList<>();

    private static volatile int workerNum;

    private WorkerManager() {}

    public static void register(int workerNum, Class<? extends Worker> workerClass) throws Exception {
        if (INIT_WORKER.compareAndSet(false, true)) {
            WorkerManager.workerNum = workerNum;

            for (int workId = 0; workId < workerNum; workId++) {
                Worker worker = workerClass.getConstructor().newInstance();
                worker.setId(workId);
                RunnerManager.addRunner(worker, worker.getWorkerName() + worker.getId(), false);
                WORKERS.put(worker.getId(), worker);
                WORK_IDS.add(workId);
            }
        }
    }

    public static int getWorkerNum() {
        return workerNum;
    }

    public static List<Integer> getWorkIds() {
        return Collections.unmodifiableList(WORK_IDS);
    }

    @SuppressWarnings("unchecked")
    public static <Element> Worker<Element> getWorker(int workerId) {
        return WORKERS.get(workerId);
    }

}
