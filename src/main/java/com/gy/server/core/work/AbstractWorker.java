package com.gy.server.core.work;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import com.gy.server.core.MessageConstant;
import com.gy.server.utils.EmbeddedLogger;

/**
 * <AUTHOR> - [Create on 2021/02/26 11:31]
 */
public abstract class AbstractWorker<Element> implements Worker<Element> {

    private int id;
    private final String workerName = this.getClass().getSimpleName() + "_";
    private final BlockingQueue<Element> requestQueue = new LinkedBlockingQueue<>(MessageConstant.WORKER_QUEUE_CAPACITY);

    @Override
    public final void setId(int id) {
        this.id = id;
    }

    public final int getId() {
        return id;
    }

    @Override
    public final String getWorkerName() {
        return workerName;
    }

    public void addTask(Element element) {
        if(!requestQueue.offer(element)) {
            EmbeddedLogger.error("worker queue is full");
            return;
        }

        if(requestQueue.size() > MessageConstant.WORKER_QUEUE_CAPACITY * 0.7) {
            EmbeddedLogger.warn(String.format("worker queue is busy, size:{%s}", requestQueue.size()));
        }

    }

    @Override
    public void runnerExecute() throws Exception {
        Element element;
        int count = 0;
        long s = System.currentTimeMillis();
        while ((element = requestQueue.poll()) != null) {
            work(element);
            if(count++ >100) {
                break;
            }
        }

        long cost = System.currentTimeMillis() - s;
        if (cost > 500) {
            EmbeddedLogger.warn(String.format("work runner too long : cost:{%s}ms", cost));
        }
    }

    @Override
    public long getRunnerInterval() {
        return 1L;
    }

    public BlockingQueue<Element> getRequestQueue() {
        return requestQueue;
    }
}
