package com.gy.server.core.work;

import com.gy.server.utils.runner.LongRunner;
import com.gy.server.utils.time.DateTimeUtil;

/**
 * <AUTHOR> - [Create on 2021/03/04 20:40]
 */
public interface Worker<Element> extends LongRunner {

    /**
     * 添加任务
     */
    void addTask(Element element);

    /**
     * 开始工作
     */
    void work(Element element) throws Exception;

    void setId(int id);

    int getId();

    String getWorkerName();

    /**
     * 默认1小时
     */
    default long getMaxExecuteTime() {
        return DateTimeUtil.MillisOfHour;
    }

}
