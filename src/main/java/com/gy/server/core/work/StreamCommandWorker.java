package com.gy.server.core.work;

import java.lang.reflect.Method;
import java.util.Objects;

import com.gy.server.annotation.AnnotationMethodCacheManager;
import com.gy.server.core.command.Message;
import com.gy.server.utils.EmbeddedLogger;

/**
 * <AUTHOR> - [Create on 2021/02/26 14:28]
 */
@SuppressWarnings("unused")
public class StreamCommandWorker extends AbstractWorker<Message> {

    @Override
    public void work(Message message) throws Exception {
        long start = System.currentTimeMillis();
        Method method = AnnotationMethodCacheManager.getMethod(message.getCommandRequest().getMethodId());
        if (Objects.isNull(method)) {
            EmbeddedLogger.error(String.format("cannot found method, method id: %s.", message.getCommandRequest().getMethodId()));
            return;
        }

        AnnotationMethodCacheManager.invoke(message.getCommandRequest().getMethodId(), null, message.getCommandRequest(), message.getRequestParams());
        message.ack();
        long cost = System.currentTimeMillis() - start;
        if (cost > 100) {
            EmbeddedLogger.warn(String.format("CommandWorker too long : cost:{%s}ms, method id: %s ", cost, message.getCommandRequest().getMethodId()));
        }
    }

}
