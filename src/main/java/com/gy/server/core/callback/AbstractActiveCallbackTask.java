package com.gy.server.core.callback;

import com.gy.server.core.command.CommandRequest;

/**
 * <AUTHOR> - [Create on 2022/08/08 11:46]
 */
public abstract class AbstractActiveCallbackTask implements ActiveCallbackTask {

    protected final CommandRequest request;

    /**
     * 默认在最后发起回调
     */
    protected boolean toCallbackAtLast = true;

    /**
     * 是否已经触发过回调
     */
    private boolean callback = false;

    public AbstractActiveCallbackTask(CommandRequest request) {
        this.request = request;
        request.setCallbackType(CallbackType.active);
    }

    @Override
    public void run() {
        execute();

        if (toCallbackAtLast) {
            toCallback();
        }
    }

    protected abstract void execute();

    @Override
    public final void toCallback() {
        if (callback) {
            throw new RuntimeException("回调多次是无效的");
        }

        callback = true;
        request.callBack();
    }

}
