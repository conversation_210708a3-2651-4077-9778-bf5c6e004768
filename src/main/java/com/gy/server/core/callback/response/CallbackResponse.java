package com.gy.server.core.callback.response;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import com.gy.server.core.command.CommandRequest;

/**
 * 回调响应
 * <AUTHOR> - [Create on 2022/02/28 09:44]
 */
public class CallbackResponse {

    /**
     * 回调ID，当回调到来时，根据此ID获取任务上下文进行调度
     */
    protected String callbackId;

    /**
     * 请求ID
     */
    protected long requestId;

    /**
     * 响应参数列表
     */
    protected List<Object> params = new CopyOnWriteArrayList<>();

    protected String remoteStreamName;
    protected int remoteServerId;

    public CallbackResponse(CommandRequest request) {
        this.callbackId = request.getCallbackId();
        this.requestId = request.getId();
        this.params.addAll(request.getCallbackParams());
        this.remoteStreamName = request.getRemoteStreamName();
        this.remoteServerId = request.getRemoteServerId();
    }

    public String getCallbackId() {
        return callbackId;
    }

    public long getRequestId() {
        return requestId;
    }

    @SuppressWarnings("unchecked")
    public <T> T getParam(int index) {
        return (T) this.params.get(index);
    }

    public List<Object> getParams() {
        return Collections.unmodifiableList(params);
    }

    public String getRemoteStreamName() {
        return remoteStreamName;
    }

    public int getRemoteServerId() {
        return remoteServerId;
    }

    public String getKey() {
        return String.valueOf(this.requestId);
    }

}
