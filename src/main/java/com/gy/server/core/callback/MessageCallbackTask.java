package com.gy.server.core.callback;

import com.gy.server.core.callback.response.CallbackResponse;

/**
 * <AUTHOR> - [Create on 2022/02/25 09:48]
 */
@SuppressWarnings("all")
public interface MessageCallbackTask {

    String getId();

    int getHashKey();

    /**
     * 回调处理
     */
    void onReceive(CallbackResponse response);

    /**
     * 任务已完全结束，最后阶段处理
     */
    void onComplete();

    /**
     * 等待至指定的时间，如果任务仍未发生回调，则说明已超时
     */
    long waitFor();

    /**
     * 超时处理
     */
    void onTimeout();

    /**
     * 批量任务的总数量
     */
    void setExpectNum(int expectNum);

    int getExpectNum();

    void incrNowNum();

    /**
     * 批量任务是否收到所有响应
     */
    boolean isBatchComplete();

    /**
     * 是否从"未结束"状态转变为"已结束"状态
     */
    boolean changeToFinish();

    /**
     * 是否 需要快速失败
     * @return
     */
    boolean isFailFast();

    /**
     * 快速失败处理逻辑接口
     * @return
     */
    void onFailFast();

}
