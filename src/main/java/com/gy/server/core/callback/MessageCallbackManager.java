package com.gy.server.core.callback;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

import com.gy.server.core.InvokeTask;
import com.gy.server.core.RegisterSystem;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.util.ThreadPoolUtil;
import com.gy.server.utils.EmbeddedLogger;

/**
 * 消息系统回调管理器
 *
 * <AUTHOR> - [Create on 2022/02/25 09:51]
 */
@SuppressWarnings("all")
public final class MessageCallbackManager {

    private static final MessageCallbackManager INSTANCE = new MessageCallbackManager();
    private static final Map<String, MessageCallbackTask> TASKS = new ConcurrentHashMap<>();
    private static final ExecutorService SINGLE_THREAD = ThreadPoolUtil.newSingleThreadPool("MessageCallbackManager-");

    private MessageCallbackManager() {
    }

    public static MessageCallbackManager getInstance() {
        return INSTANCE;
    }

    public static int getTaskSize() {
        return TASKS.size();
    }

    public static void addTask(MessageCallbackTask task) {

        //是否需要快速失败
        if (RegisterSystem.failFast.get() && task.isFailFast()) {
            task.onFailFast();
            return;
        }

        if (task.isBatchComplete()) {
            RegisterSystem.callbackInvokeType.invoke(new InvokeTask(() -> {
                long start = System.currentTimeMillis();
                task.onComplete();
                long cost = System.currentTimeMillis() - start;
                if (cost > 100) {
                    EmbeddedLogger.warn("MsgCallback task cost too much time, cost: {}ms, task: {}", cost, task.getClass().getName());
                }
            }, task.getHashKey()) {
            });
        } else {
            TASKS.put(task.getId(), task);
        }
    }

    public static boolean setTaskExpectNum(String uuid, int expectNum) {
        boolean result = false;

        MessageCallbackTask task = TASKS.get(uuid);
        if (Objects.nonNull(task)) {
            task.setExpectNum(expectNum);
            result = true;
        }

        return result;
    }

    public static void runTask(CallbackResponse response, Runnable afterTaskInvoked) {
        MessageCallbackTask task = TASKS.get(response.getCallbackId());
        if (Objects.nonNull(task)) {
            RegisterSystem.callbackInvokeType.invoke(new InvokeTask(() -> {
                long start = System.currentTimeMillis();
                task.onReceive(response);
                if (task.isBatchComplete() && task.changeToFinish()) {
                    task.onComplete();
                    TASKS.remove(response.getCallbackId());
                }

                afterTaskInvoked.run();
                long cost = System.currentTimeMillis() - start;
                if (cost > 100) {
                    EmbeddedLogger.warn("MsgCallback task cost too much time, cost: {}ms, task: {}", cost, task.getClass().getName());
                }
            }, task.getHashKey()) {
            });
        }else{
            afterTaskInvoked.run();
        }
    }

    public static void startup() {
        SINGLE_THREAD.execute(() -> {
            while (RegisterSystem.serverStartupFlag == null || RegisterSystem.serverStartupFlag.get()) {
                try {
                    long now = System.currentTimeMillis();

                    for (Map.Entry<String, MessageCallbackTask> entry : TASKS.entrySet()) {
                        String key = entry.getKey();
                        MessageCallbackTask task = entry.getValue();

                        long waitForTime = task.waitFor();
                        if (now >= waitForTime && task.changeToFinish()) {
                            RegisterSystem.callbackInvokeType.invoke(new InvokeTask(task::onTimeout, task.getHashKey()) {
                            });
                            TASKS.remove(key);
                        }
                    }

                    TimeUnit.SECONDS.sleep(1);
                } catch (Exception e) {
                    EmbeddedLogger.error(e);
                }
            }
        });
    }

    public static void shutdown() {
        SINGLE_THREAD.shutdownNow();
    }

}
