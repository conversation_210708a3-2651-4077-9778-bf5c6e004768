package com.gy.server.core.callback;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.util.MessageUtil;
import com.gy.server.utils.time.DateTimeUtil;

/**
 * <AUTHOR> - [Create on 2022/02/25 17:59]
 */
@SuppressWarnings("unused")
public abstract class AbstractMessageCallbackTask implements MessageCallbackTask {

    protected String id = MessageUtil.genNextCallbackId();
    protected long overDueTime = System.currentTimeMillis() + waitTimeOffset();
    private final Map<String, CallbackResponse> responses = new ConcurrentHashMap<>();

    private volatile int expectNum = 1;
    private final AtomicInteger nowNum = new AtomicInteger(0);
    private final AtomicBoolean finish = new AtomicBoolean(false);

    @Override
    public String getId() {
        return id;
    }

    @Override
    public final void onReceive(CallbackResponse response) {
        responses.put(String.valueOf(response.getRemoteServerId()), response);
        subOnReceive(response);
        incrNowNum();
    }

    @SuppressWarnings("unused")
    protected void subOnReceive(CallbackResponse response) {
    }

    @Override
    public final void onComplete() {
        subOnComplete(responses);
    }

    @SuppressWarnings("unused")
    protected void subOnComplete(Map<String, CallbackResponse> responses) {
    }

    @Override
    public long waitFor() {
        return overDueTime;
    }

    /**
     * 等待时间偏移量
     */
    protected long waitTimeOffset() {
        return DateTimeUtil.MillisOfSecond * 30;
    }

    @Override
    public final void onTimeout() {
        subOnTimeout(responses);
    }

    protected abstract void subOnTimeout(Map<String, CallbackResponse> responses);

    @Override
    public void setExpectNum(int expectNum) {
        this.expectNum = expectNum;
    }

    @Override
    public int getExpectNum() {
        return expectNum;
    }

    @Override
    public final void incrNowNum() {
        this.nowNum.incrementAndGet();
    }

    @Override
    public boolean isBatchComplete() {
        return this.nowNum.get() >= this.expectNum;
    }

    @Override
    public final boolean changeToFinish() {
        return finish.compareAndSet(false, true);
    }

    @Override
    public boolean isFailFast() {
        return false;
    }

    @Override
    public void onFailFast() {

    }
}
