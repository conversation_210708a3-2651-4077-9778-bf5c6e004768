package com.gy.server.core.callback.response;

import com.gy.server.core.command.PlayerCommandRequest;

/**
 * <AUTHOR> - [Create on 2022/02/28 10:03]
 */
public class PlayerCallbackResponse extends CallbackResponse {

    private long playerId;
    private long timestamp;

    public PlayerCallbackResponse(PlayerCommandRequest request) {
        super(request);

        this.playerId = request.getPlayerId();
        this.timestamp = request.getTimestamp();
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

}
