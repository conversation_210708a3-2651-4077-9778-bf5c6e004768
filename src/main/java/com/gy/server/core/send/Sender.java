package com.gy.server.core.send;

import com.gy.server.core.MessageServerType;
import com.gy.server.core.command.Message;

import java.util.Collection;

/**
 * <AUTHOR> - [Create on 2021/02/24 17:50]
 */
public interface Sender {

    /**
     * 单播
     */
    void sendToSingle(Message message, MessageServerType serverType, int id);

    /**
     * 单播 + 随机
     */
    void sendToAny(Message message, MessageServerType serverType);

    /**
     * 多播 + 指定
     */
    void sendToMany(Message message, MessageServerType serverType, Collection<Integer> ids);

    /**
     * 广播
     */
    void sendToAll(Message message, MessageServerType serverType);

}
