package com.gy.server.core.send;

import com.gy.server.core.MessageServerType;
import com.gy.server.core.RegisterSystem;
import com.gy.server.core.command.Message;
import com.gy.server.core.dispatch.StreamDispatcher;
import com.gy.server.utils.qps.QPSManager;

import java.util.Collection;

/**
 * @program: message-nats
 * @description: 发送父类
 * @author: <PERSON><PERSON>
 * @create: 2025/6/11
 **/
public abstract class AbstractStreamSender implements Sender{

    private StreamDispatcher dispatcher;

    public AbstractStreamSender(StreamDispatcher dispatcher) {
        this.dispatcher = dispatcher;
    }

    @Override
    public void sendToSingle(Message message, MessageServerType serverType, int id) {
        if(serverType == RegisterSystem.serverType && id == RegisterSystem.serverId){
            //自己节点，直接处理
            dispatcher.dispatch(message);
        }else{
            //非自己节点，转发
            subSendToSingle(message, serverType, id);
        }

        //统计发送次数
        QPSManager.count("rpc.sendToSingle." + message.getCommandRequest().getMethodId());
        QPSManager.count("rpc.sendToSingle.all");
    }

    public abstract void subSendToSingle(Message message, MessageServerType serverType, int id);

    @Override
    public void sendToAny(Message message, MessageServerType serverType) {

        subSendToAny(message, serverType);

        QPSManager.count("rpc.sendToAny." + message.getCommandRequest().getMethodId());
        QPSManager.count("rpc.sendToAny.all");
    }

    public abstract void subSendToAny(Message message, MessageServerType serverType);

    @Override
    public void sendToMany(Message message, MessageServerType serverType, Collection<Integer> ids) {
        subSendToMany(message, serverType, ids);
        QPSManager.count("rpc.sendToMany." + message.getCommandRequest().getMethodId());
        QPSManager.count("rpc.sendToMany.all");
    }

    public abstract void subSendToMany(Message message, MessageServerType serverType, Collection<Integer> ids);

    @Override
    public void sendToAll(Message message, MessageServerType serverType) {

        subSendToAll(message, serverType);
        QPSManager.count("rpc.sendToAll." + message.getCommandRequest().getMethodId());
        QPSManager.count("rpc.sendToAll.all");
    }

    public abstract void subSendToAll(Message message, MessageServerType serverType);
}
