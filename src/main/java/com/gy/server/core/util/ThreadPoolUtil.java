package com.gy.server.core.util;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;

/**
 * <AUTHOR> - [Create on 2021/03/04 10:36]
 */
public final class ThreadPoolUtil {

    private ThreadPoolUtil() {}

    public static ExecutorService newSingleThreadPool(String namePattern) {
        ThreadFactory threadFactory = new BasicThreadFactory.Builder()
                .namingPattern(namePattern)
                .build();

        return new ThreadPoolExecutor(1, 1, 30, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), threadFactory);
    }

}
