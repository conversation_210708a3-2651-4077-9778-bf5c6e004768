package com.gy.server.core.util.serial;

import com.gy.server.core.util.SerialUtil;
import org.jboss.marshalling.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * @program: message-nats
 * @description:
 * @author: <PERSON><PERSON>
 * @create: 2025/6/26
 **/
public class RiverMarshallerSerialUtil implements SerialUtil {
    private static final MarshallerFactory factory = Marshalling.getProvidedMarshallerFactory("river");
    private static final MarshallingConfiguration config = new MarshallingConfiguration();

    static {
        config.setVersion(3); // 推荐设置版本
    }

    // 线程安全的 marshaller
    private static final ThreadLocal<Marshaller> marshallerThreadLocal = ThreadLocal.withInitial(() -> {
        try {
            return factory.createMarshaller(config);
        } catch (IOException e) {
            throw new RuntimeException("Failed to create marshaller", e);
        }
    });

    // 线程安全的 unmarshaller
    private static final ThreadLocal<Unmarshaller> unmarshallerThreadLocal = ThreadLocal.withInitial(() -> {
        try {
            return factory.createUnmarshaller(config);
        } catch (IOException e) {
            throw new RuntimeException("Failed to create unmarshaller", e);
        }
    });

    @Override
    public byte[] encode(Object obj) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Marshaller marshaller = marshallerThreadLocal.get();
        try {
            marshaller.start(Marshalling.createByteOutput(baos));
            marshaller.writeObject(obj);
            marshaller.finish();
            return baos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Serialization error", e);
        }
    }

    @Override
    public Object decodeMessage(byte[] bytes) {
        ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
        Unmarshaller unmarshaller = unmarshallerThreadLocal.get();
        try {
            unmarshaller.start(Marshalling.createByteInput(bais));
            Object obj = unmarshaller.readObject();
            unmarshaller.finish();
            return obj;
        } catch (IOException | ClassNotFoundException e) {
            throw new RuntimeException("Deserialization error", e);
        }
    }
}
