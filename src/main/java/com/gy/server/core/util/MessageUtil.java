package com.gy.server.core.util;

import java.util.concurrent.atomic.AtomicLong;

import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.callback.response.PlayerCallbackResponse;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;

/**
 * <AUTHOR> - [Create on 2022/02/25 10:15]
 */
public final class MessageUtil {

    private static final AtomicLong REQUEST_ID = new AtomicLong(0);
    private static final AtomicLong MESSAGE_CALLBACK_ID = new AtomicLong(0);

    private MessageUtil() {}

    public static long genNextRequestId() {
        return REQUEST_ID.incrementAndGet();
    }

    public static String genNextCallbackId() {
        return String.valueOf(MESSAGE_CALLBACK_ID.incrementAndGet());
    }

    public static CallbackResponse genResponse(CommandRequest request) {
        CallbackResponse result;

        if (request instanceof PlayerCommandRequest) {
            result = new PlayerCallbackResponse((PlayerCommandRequest) request);
        } else if (request instanceof ServerCommandRequest) {
            result = new CallbackResponse(request);
        } else {
            throw new UnsupportedOperationException(String.format("cannot generate CallbackResponse, request type unsupported: %s.", request.getClass()));
        }

        return result;
    }

}
