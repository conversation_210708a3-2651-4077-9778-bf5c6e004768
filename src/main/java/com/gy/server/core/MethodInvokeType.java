package com.gy.server.core;

/**
 * 方法被调用类型
 * <AUTHOR> - [Create on 2021/02/24 16:45]
 */
@SuppressWarnings("unused")
public enum MethodInvokeType {

    sync,                           // 通常在一个被称作主线程的地方调用
    async,                          // 常见于在一个线程池下调用
    free,                           // 自由选择方式
    other,                          // 其他

    ;

    private InvokeTypeOperator operator;

    public void setOperator(InvokeTypeOperator operator) {
        this.operator = operator;
    }

    public void invoke(InvokeTask task) {
        operator.runTask(task);
    }

}
