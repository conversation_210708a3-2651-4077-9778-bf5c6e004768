package com.gy.server.core.receive;

import com.gy.server.core.command.Message;
import com.gy.server.core.dispatch.StreamDispatcher;
import com.gy.server.core.util.ThreadPoolUtil;
import com.gy.server.utils.EmbeddedLogger;

import java.util.Arrays;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> - [Create on 2021/02/25 17:57]
 */
@SuppressWarnings({"unused", "rawtypes"})
public abstract class AbstractStreamReceiver implements StreamReceiver{

    protected final ExecutorService readThreadPool = ThreadPoolUtil.newSingleThreadPool("receiverRead-");

    private volatile StreamDispatcher dispatcher;

    protected String streamName;
    protected String subjectName;
    protected String consumerGroup;
    protected String consumerName;

    public AbstractStreamReceiver(String topicName, String subjectName, String consumerGroup, String consumerName) {
        this.streamName = topicName;
        this.subjectName = subjectName;
        this.consumerGroup = consumerGroup;
        this.consumerName = consumerName;
    }

    @Override
    public final void startup(StreamDispatcher dispatcher, Object... params) {
        this.dispatcher = dispatcher;
        EmbeddedLogger.info(String.format("%s startup, params: %s.", this.getClass().getSimpleName(), Arrays.toString(params)));
        subStartup(params);
    }

    /**
     * 子类启动方法，默认空实现
     */
    protected abstract void subStartup(Object... params);

    public void receiveMessage(Message message){
        dispatcher.dispatch(message);
    }

    @Override
    public final void shutdown() throws InterruptedException {
        EmbeddedLogger.info(String.format("%s shutdown.", this.getClass().getSimpleName()));

        EmbeddedLogger.info("readThreadPool shutdown.");
        readThreadPool.shutdownNow();

        subShutdown();

        int waitTimes = 30;
        while (waitTimes-- > 0) {
            if (!readThreadPool.isTerminated()) {
                TimeUnit.SECONDS.sleep(1);
                continue;
            }

            break;
        }
    }

    /**
     * 子类关闭方法，默认空实现
     */
    protected abstract void subShutdown();

}
