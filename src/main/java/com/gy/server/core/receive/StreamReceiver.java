package com.gy.server.core.receive;


import com.gy.server.core.dispatch.StreamDispatcher;

/**
 * <AUTHOR> - [Create on 2021/02/25 17:16]
 */
@SuppressWarnings("unused")
public interface StreamReceiver {

    /**
     * 启动
     * @param dispatcher 分发器
     * @param params 参数数组
     */
    void startup(StreamDispatcher dispatcher, Object... params);

    /**
     * 关闭
     */
    void shutdown() throws InterruptedException;

}
