package com.gy.server.core;

import com.gy.server.core.send.Sender;
import com.gy.server.core.util.SerialUtil;
import com.gy.server.core.util.serial.RiverMarshallerSerialUtil;

import java.util.function.Supplier;

/**
 * <AUTHOR> - [Create on 2022/02/25 18:02]
 */
@SuppressWarnings("all")
public class RegisterSystem {

    public static Supplier<Boolean> serverStartupFlag;

    /**
     * 回调渠道名
     */
    @Deprecated
    public static String callbackStreamName;

    /**
     * 回调服务ID
     */
    @Deprecated
    public static int callbackServerId;

    public static int serverId;
    public static MessageServerType serverType;

    /**
     * 回调发生时的调用方式
     */
    public static MethodInvokeType callbackInvokeType;

    /**
     * 通讯快速失败标记
     */
    public static Supplier<Boolean> failFast = () -> false;

    public static Sender sender;

    public static String evnName = "dev";

    public static SerialUtil serialUtil = new RiverMarshallerSerialUtil();

    public static void registerServerStartupFlag(Supplier<Boolean> serverStartupFlag) {
        RegisterSystem.serverStartupFlag = serverStartupFlag;
    }

    public static void registerCallbackStreamName(String callbackStreamName) {
        RegisterSystem.callbackStreamName = callbackStreamName;
    }

    public static void registerCallbackServerId(int callbackServerId) {
        RegisterSystem.callbackServerId = callbackServerId;
    }

    public static void registerCallbackInvokeType(MethodInvokeType callbackInvokeType) {
        RegisterSystem.callbackInvokeType = callbackInvokeType;
    }

    public static void registerFailFast(Supplier<Boolean> failFast) {
        RegisterSystem.failFast = failFast;
    }

}
