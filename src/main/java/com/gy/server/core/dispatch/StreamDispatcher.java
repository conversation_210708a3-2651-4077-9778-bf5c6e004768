package com.gy.server.core.dispatch;

import com.gy.server.core.MessageConstant;
import com.gy.server.core.command.Message;


/**
 * 消息分发器
 * <AUTHOR> - [Create on 2021/03/04 20:10]
 */
public interface StreamDispatcher {

    /**
     * 分发消息
     */
    @SuppressWarnings("rawtypes")
    void dispatch(Message msg);

    default boolean isOvertime(long sendTimestamp) {
        return (System.currentTimeMillis() - sendTimestamp) / 1000 / 60 >= MessageConstant.DISCARD_TIME;
    }

}
