package com.gy.server.core.dispatch;

import com.gy.server.annotation.AnnotationMethodCacheManager;
import com.gy.server.core.InvokeTask;
import com.gy.server.core.MessageConstant;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.RegisterSystem;
import com.gy.server.core.callback.CallbackType;
import com.gy.server.core.callback.MessageCallbackManager;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.*;
import com.gy.server.core.exception.MessageException;
import com.gy.server.core.exception.MessageExceptionType;
import com.gy.server.core.util.MessageUtil;
import com.gy.server.utils.EmbeddedLogger;
import com.gy.server.utils.compressor.CompressorUtil;
import com.gy.server.utils.qps.QPSManager;
import org.apache.commons.lang3.SerializationUtils;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.zip.DataFormatException;

/**
 * 按照调用方法的不同，转发到不同的线程进行处理的分发器
 *
 * <AUTHOR> - [Create on 2021/03/04 20:47]
 */
@SuppressWarnings("unused")
public class StreamCommandByInvokeTypeDispatcher implements StreamDispatcher{

    private static Function<Long,Boolean> onlineChecker;

    private static String offlineMsgPrefix = null;

    /**
     * 重量级任务下次可接受时间
     * 重量级任务如果数量比较多，会导致工作线程池阻塞，消息队列堵塞。权衡之下，舍弃高频重量级任务，正常也不应该出现高频且重量级的任务。
     * 两种可能出现的原因：
     *    1. 任务本身重量级，需要优化解决
     *    2. 当前遇到瓶颈，比如超高并发或者redis不可用造成任务超时
     */
    public static Map<String, Long> heavyTaskNextAcceptTime = new ConcurrentHashMap<>();

    public static void setOnlineChecker(Function<Long, Boolean> onlineChecker) {
        StreamCommandByInvokeTypeDispatcher.onlineChecker = onlineChecker;
    }

    public static void setOfflineMsgPrefix(String offlineMsgPrefix) {
        StreamCommandByInvokeTypeDispatcher.offlineMsgPrefix = offlineMsgPrefix;
    }

    @SuppressWarnings("rawtypes")
    @Override
    public void dispatch(Message msg) {
        CommandRequest commandRequest = msg.getCommandRequest();
        try {
            // 可被丢弃的请求
            if (commandRequest.isCanDiscard()
                    // 超时
                    && isOvertime(commandRequest.getSendTimestamp())) {
                // 直接确认这条消息，不处理
                msg.ack();

                // 日志太多，当前状态调整为不打印，可待观察
                //EmbeddedLogger.warn(String.format("discard message, commandRequest: %s, params: %s.", commandRequest, Arrays.toString(objects)));
                return;
            }

            if (commandRequest.getCommandType() == CommandType.callback) {

                QPSManager.count("redis.callback."+commandRequest.getMethodId());
                QPSManager.count("redis.callback.all");

                try {
                    if (!commandRequest.getMessageExceptionType().equals(MessageExceptionType.TYPE_NONE)) {
                        EmbeddedLogger.error(String.format("error occurred before callback, type: %s.", commandRequest.getMessageExceptionType()));
                    }

                    CallbackResponse response = MessageUtil.genResponse(commandRequest);
                    MessageCallbackManager.runTask(response, () -> msg.ack());
                } catch (Exception e) {
                    EmbeddedLogger.error(e);
                }

                return;
            }

            QPSManager.count("redis.call."+commandRequest.getMethodId());
            QPSManager.count("redis.call.all");

            Method method = AnnotationMethodCacheManager.getMethod(commandRequest.getMethodId());
            if (Objects.isNull(method)) {
                EmbeddedLogger.error(String.format("cannot found method, method id: %s.", commandRequest.getMethodId()));
                msg.ack();
                return;
            }

            //重量级任务频率检查
            if(heavyTaskNextAcceptTime.containsKey(commandRequest.getMethodId())){
                long nextAcceptTime = heavyTaskNextAcceptTime.get(commandRequest.getMethodId());
                if(System.currentTimeMillis() < nextAcceptTime){
                    EmbeddedLogger.error(String.format("method %s is heavy task, next accept time: %s.", commandRequest.getMethodId(), nextAcceptTime));
                    msg.ack();
                    return;
                }
            }

            CommandRequestParams params = msg.getRequestParams();

            // 玩家在线才能调用的方法
            if(AnnotationMethodCacheManager.getPlayerOnlineOnly(method)){
                if(commandRequest instanceof  PlayerCommandRequest){
                    PlayerCommandRequest playerCommandRequest = (PlayerCommandRequest) commandRequest;
                    if(onlineChecker == null){
                        //没有初始化在线检测器
                        EmbeddedLogger.error(String.format("method %s is player online only, but onlineChecker is null.", commandRequest.getMethodId()));
                        msg.ack();
                        return;
                    }
                    if(offlineMsgPrefix == null){
                        //没有初始化离线消息前缀
                        EmbeddedLogger.error(String.format("method %s is player online only, but offlineMsgPrefix is null.", commandRequest.getMethodId()));
                        msg.ack();
                        return;
                    }
                    if (commandRequest.getCallbackType() == CallbackType.passive
                            && !commandRequest.getCallbackId().equals(MessageConstant.DEFAULT_CALLBACK_ID)){
                        //不能为自动回调的方法
                        EmbeddedLogger.error(String.format("method %s is player online only, cannot be callback passive.", commandRequest.getMethodId()));
                        msg.ack();
                        return;
                    }

                    if(!onlineChecker.apply(playerCommandRequest.getPlayerId())){
                        //玩家不在线，存储到离线消息
                        CommondDB cmd = new CommondDB(playerCommandRequest, msg.getRequestParams().getParams());
                        byte[] data = SerializationUtils.serialize(cmd);
                        data = CompressorUtil.deflate().compress(data);

                        //TODO 存储到持久化结构中，等待玩家下线后再执行

//                        String key = offlineMsgPrefix + playerCommandRequest.getPlayerId();
//                        RedissonClient redissonClient = RedissonHelper.getMessageClient();
//                        RList queue = redissonClient.getList(key, ByteArrayCodec.INSTANCE);
//                        queue.addAsync(data);
//
//                        //五十分之一几率trim
//                        if(MathUtil.randomInt(50) == 0){
//                            RList list = redissonClient.getList(key, ByteArrayCodec.INSTANCE);
//                            list.trimAsync(-200, -1);//保留最近的200条记录
//                        }
//
//                        receiveStrategy.ackAsync(messageId);
                        msg.ack();
                        throw new RuntimeException("not implement");
                        //return;
                    }else {
                        //继续后面的调用
                    }

                }else{
                    EmbeddedLogger.error(String.format("method %s is player online only, but request is not PlayerCommandRequest.", commandRequest.getMethodId()));
                    return;
                }
            }

            MethodInvokeType invokeType = AnnotationMethodCacheManager.getInvokeType(method);
            invokeType.invoke(new InvokeTask(() -> {
                long start = System.currentTimeMillis();
                try {
                    AnnotationMethodCacheManager.invoke(commandRequest.getMethodId(), null, commandRequest, params);
                    msg.ack();
                } catch (MessageException e) {
                    commandRequest.setMessageExceptionType(e.getType());
                    msg.ack();
                } catch (Exception e) {
                    EmbeddedLogger.error(e);
                    msg.ack();
                    commandRequest.setMessageExceptionType(MessageExceptionType.TYPE_UNKNOWN);
                }

                // 需要回调
                if (commandRequest.getCallbackType() == CallbackType.passive
                        && !commandRequest.getCallbackId().equals(MessageConstant.DEFAULT_CALLBACK_ID)) {

                    commandRequest.callBack();
                }
                long cost = System.currentTimeMillis() - start;
                if (cost > 100) {
                    EmbeddedLogger.warn(String.format("MsgCall cost too much time, method: %s, cost: %s.", commandRequest.getMethodId(), cost));

                    if(cost > 5 * 1000){
                        heavyTaskNextAcceptTime.put(commandRequest.getMethodId(), System.currentTimeMillis() + cost);
                    }
                }
            }, commandRequest.getHashKey()) {});
        } catch (Exception e) {
            EmbeddedLogger.error(e);
        }
    }

    /**
     * 执行玩家的离线消息，登陆时调用。注意异步线程调用。
     * @param pid 角色id
     */
    public void executeOfflineCommand(long pid){
        if(true){
            return;
        }
        String key = offlineMsgPrefix + pid;

        //TODO 从持久化队列中读取数据，并执行
//        RedissonClient redissonClient = RedissonHelper.getMessageClient();
//        RQueue queue = redissonClient.getQueue(key, ByteArrayCodec.INSTANCE);
        List<byte[]> data = null;
        int count = 0;
        while (/*(data = queue.poll(30)) != null && */data.size() > 0){
            for(byte[] bytes : data){
                count++;
                try {
                    bytes = CompressorUtil.deflate().decompress(bytes);
                    CommondDB cmd = SerializationUtils.deserialize(bytes);

                    CommandRequest commandRequest = cmd.getReq();
                    Method method = AnnotationMethodCacheManager.getMethod(commandRequest.getMethodId());
                    if (Objects.isNull(method)) {
                        EmbeddedLogger.error(String.format("cannot found method, method id: %s.", commandRequest.getMethodId()));
                        return;
                    }
                    MethodInvokeType invokeType = AnnotationMethodCacheManager.getInvokeType(method);
                    invokeType.invoke(new InvokeTask(() -> {
                        long start = System.currentTimeMillis();
                        try {
                            CommandRequestParams params = new CommandRequestParams(cmd.getParams());
                            AnnotationMethodCacheManager.invoke(commandRequest.getMethodId(), null, commandRequest, params);
                        } catch (MessageException e) {
                            commandRequest.setMessageExceptionType(e.getType());
                        } catch (Exception e) {
                            EmbeddedLogger.error(e);
                            commandRequest.setMessageExceptionType(MessageExceptionType.TYPE_UNKNOWN);
                        }

                        long cost = System.currentTimeMillis() - start;
                        if (cost > 100) {
                            EmbeddedLogger.warn(String.format("MsgCall cost too much time, method: %s, cost: %s.", commandRequest.getMethodId(), cost));
                        }
                    }, commandRequest.getHashKey()) {});
                } catch (IOException e) {
                    throw new RuntimeException(e);
                } catch (DataFormatException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


}
