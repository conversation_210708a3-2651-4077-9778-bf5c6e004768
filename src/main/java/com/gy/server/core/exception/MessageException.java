package com.gy.server.core.exception;

/**
 * 消息系统主动抛出行为的异常
 * <AUTHOR> - [Create on 2022/03/18 11:13]
 */
public class MessageException extends RuntimeException {

    private static final long serialVersionUID = 3923480231582415870L;

    private String type;

    public MessageException(String type) {
        this.type = type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setType(int type) {
        this.type = String.valueOf(type);
    }

    public String getType() {
        return type;
    }

}
