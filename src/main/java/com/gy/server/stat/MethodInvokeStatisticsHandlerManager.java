package com.gy.server.stat;

import java.util.Optional;

/**
 * 方法调用统计服务管理器
 * <AUTHOR> - [Create on 2021/03/06 11:03]
 */
@SuppressWarnings("unused")
public final class MethodInvokeStatisticsHandlerManager {

    private static MethodInvokeStatisticsHandler handler;

    private MethodInvokeStatisticsHandlerManager() {}

    public static void register(MethodInvokeStatisticsHandler handler) {
        MethodInvokeStatisticsHandlerManager.handler = handler;
    }

    public static void ststistics(String methodId, long costTime) {
        Optional.ofNullable(handler).ifPresent(h -> h.statistics(methodId, costTime));
    }

}
