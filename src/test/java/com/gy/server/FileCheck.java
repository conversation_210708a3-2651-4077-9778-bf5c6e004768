package com.gy.server;

import java.io.File;

/**
 * @program: message-nats
 * @description:
 * @author: <PERSON><PERSON>
 * @create: 2025/6/13
 **/
public class FileCheck {
    private static final long SIZE_LIMIT = 512 * 1024 * 1024; // 4GB


    public static void main(String[] args) {
//        if (args.length == 0) {
//            System.err.println("请提供要检查的目录路径。");
//            return;
//        }

        File folder = new File("D:\\xWorks\\xy\\fanti\\fanti-client");
        if (!folder.exists() || !folder.isDirectory()) {
            System.err.println("提供的路径不是有效的文件夹：" + args[0]);
            return;
        }

        System.out.println("开始检查目录: " + folder.getAbsolutePath());
        checkFolder(folder);
    }

    public static void checkFolder(File folder) {
        File[] files = folder.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isDirectory()) {
                checkFolder(file); // 递归进入子目录
            } else {
                if (file.length() > SIZE_LIMIT) {
                    System.out.printf("发现大文件: %s (%.2f GB)%n", file.getAbsolutePath(), file.length() / (1024.0 * 1024 * 1024));
                }
            }
        }
    }
}
