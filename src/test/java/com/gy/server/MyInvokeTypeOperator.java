package com.gy.server;

/**
 * <AUTHOR> - [Create on 2022/02/24 20:42]
 */
public interface MyInvokeTypeOperator {

    void runTask(InvokeTask task);

}

enum MyInvokeType {

    free,
    ;

    private MyInvokeTypeOperator operator;

    public void setOperator(MyInvokeTypeOperator operator) {
        this.operator = operator;
    }

    public void invoke(InvokeTask task) {
        operator.runTask(task);
    }

}

abstract class InvokeTask {

    public final Runnable runnable;
    public final int hashKey;

    public InvokeTask(Runnable runnable, int hashKey) {
        this.runnable = runnable;
        this.hashKey = hashKey;
    }

}
