package com.gy.server;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;

/**
 * <AUTHOR> - [Create on 2021/03/04 17:27]
 */
@MessageServiceBean(messageServerType = MessageServerType.game)
public class MyTestFristCommandService {

    @MessageMethod(invokeType = MethodInvokeType.async)
    public static void firstTest(ServerCommandRequest request, CommandRequestParams params) {

    }

    @MessageMethod(invokeType = MethodInvokeType.sync)
    public static void secondTest(PlayerCommandRequest request, CommandRequestParams params) {

    }

}
