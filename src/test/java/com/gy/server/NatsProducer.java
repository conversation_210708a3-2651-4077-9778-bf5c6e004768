package com.gy.server;

import com.google.common.util.concurrent.RateLimiter;
import com.gy.server.core.MessageConstant;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.RegisterSystem;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.CommandUtil;
import com.gy.server.core.command.Message;
import com.gy.server.core.dispatch.StreamCommandByInvokeTypeDispatcher;
import com.gy.server.core.dispatch.StreamDispatcher;
import com.gy.server.core.nats.NatsHelper;
import com.gy.server.core.nats.NatsSender;
import com.gy.server.core.receive.StreamReceiver;
import com.gy.server.core.send.Sender;
import com.gy.server.utils.qps.QPSManager;

import java.util.Arrays;
import java.util.List;

/**
 * @program: message-nats
 * @description: 测试
 * @author: <PERSON><PERSON>
 * @create: 2025/6/11
 **/
public class NatsProducer {

    public static void main(String[] args) throws Exception {
        new Thread(() -> {
            while (true) {
                QPSManager.getInstance().tick();
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();

        NatsHelper.init("nats://tl.shangua.com:4022", "admin", "password123");
        RegisterSystem.serverId = 1002;
        RegisterSystem.serverType = MessageServerType.game;
        RegisterSystem.evnName = "test";

        StreamDispatcher dispatcher = new StreamCommandByInvokeTypeDispatcher();

        startReceiver(dispatcher);

        Sender sender = new NatsSender(dispatcher);

        sendData(sender);


        Thread.sleep(5000);
    }

    public static void sendData(Sender sender){

        RateLimiter rateLimiter = RateLimiter.create(1);
        for (int i = 0; i < 100; i++) {
            rateLimiter.acquire();
            CommandRequest req = CommandUtil.newPlayerCommandRequest(MessageConstant.DEFAULT_HASH_KEY,"abc.def", RegisterSystem.serverType, RegisterSystem.serverId,
                    System.currentTimeMillis(), 10010000L, false);
            CommandRequestParams params = new CommandRequestParams();
            params.setParams(new Object[] {"hello world",123,System.currentTimeMillis()});
            Message msg = new Message(req,params);
            sender.sendToSingle(msg, MessageServerType.game, 1001);
        }
    }

    public static void startReceiver(StreamDispatcher dispatcher){
        try {
            List<StreamReceiver> receiverList = NatsHelper.createReceiver(RegisterSystem.serverType, RegisterSystem.serverId);
            receiverList.stream().forEach(r -> r.startup(dispatcher));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
