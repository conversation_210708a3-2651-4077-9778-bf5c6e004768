package com.gy.server;

import com.gy.server.core.MessageConstant;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.RegisterSystem;
import com.gy.server.core.command.*;
import com.gy.server.core.dispatch.StreamCommandByInvokeTypeDispatcher;
import com.gy.server.core.dispatch.StreamDispatcher;
import com.gy.server.core.nats.NatsHelper;
import com.gy.server.core.nats.NatsSender;
import com.gy.server.core.receive.StreamReceiver;
import com.gy.server.core.send.Sender;
import com.gy.server.utils.qps.QPSManager;

import java.util.List;

/**
 * @program: message-nats
 * @description: 测试
 * @author: <PERSON><PERSON>
 * @create: 2025/6/11
 **/
public class NatsCosumer {

    public static void main(String[] args) throws Exception {
        new Thread(() -> {
            while (true) {
                QPSManager.getInstance().tick();
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();


        NatsHelper.init("nats://************:4222");
        RegisterSystem.serverId = 1001;
        RegisterSystem.serverType = MessageServerType.game;

        StreamDispatcher dispatcher = new StreamCommandByInvokeTypeDispatcher();

        startReceiver(dispatcher);

        Thread.sleep(10000000);
    }

    public static void startReceiver(StreamDispatcher dispatcher){
        try {
            List<StreamReceiver> receiverList = NatsHelper.createReceiver(RegisterSystem.serverType, RegisterSystem.serverId);
            receiverList.stream().forEach(r -> r.startup(dispatcher));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
