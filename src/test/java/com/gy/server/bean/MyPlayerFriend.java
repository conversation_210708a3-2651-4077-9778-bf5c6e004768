package com.gy.server.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;

/**
 * <AUTHOR> - [Create on 2021/03/08 18:24]
 */
@ProtobufClass
public class MyPlayerFriend implements Serializable {

    @Ignore
    private static final long serialVersionUID = 4037414629625463684L;

    @Protobuf(order = 1)
    private long playerId;

    @Protobuf(order = 2)
    private List<Long> applys = new ArrayList<>();                          // 申请列表

    @Protobuf(order = 3)
    private List<Long> friends = new ArrayList<>();                         // 好友列表

    @Protobuf(order = 4)
    private List<Long> blacklist = new ArrayList<>();                       // 黑名单

    @Protobuf(order = 5)
    private Set<Long> sendFriends = new HashSet<>();                        // 已发送给这些好友体力了

    @Protobuf(order = 6)
    private Set<Long> receiveFriends = new HashSet<>();                     // 来自于这些好友发送的体力

    @Protobuf(order = 7)
    private int powerReceiveNum;                                            // 当前已领取的体力数量

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public List<Long> getApplys() {
        return applys;
    }

    public void setApplys(List<Long> applys) {
        this.applys = applys;
    }

    public List<Long> getFriends() {
        return friends;
    }

    public void setFriends(List<Long> friends) {
        this.friends = friends;
    }

    public List<Long> getBlacklist() {
        return blacklist;
    }

    public void setBlacklist(List<Long> blacklist) {
        this.blacklist = blacklist;
    }

    public Set<Long> getSendFriends() {
        return sendFriends;
    }

    public void setSendFriends(Set<Long> sendFriends) {
        this.sendFriends = sendFriends;
    }

    public Set<Long> getReceiveFriends() {
        return receiveFriends;
    }

    public void setReceiveFriends(Set<Long> receiveFriends) {
        this.receiveFriends = receiveFriends;
    }

    public int getPowerReceiveNum() {
        return powerReceiveNum;
    }

    public void setPowerReceiveNum(int powerReceiveNum) {
        this.powerReceiveNum = powerReceiveNum;
    }

}
